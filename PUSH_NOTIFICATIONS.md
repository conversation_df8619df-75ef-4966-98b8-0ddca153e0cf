# Push Notifications Setup Guide

This guide explains how to set up and use the push notification system in Sphere Web.

## Overview

The push notification system allows users to receive notifications even when the app is closed. It integrates with the existing notification infrastructure and uses the browser's native push notification API.

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Go Backend    │    │   Browser/<PERSON><PERSON>    │    │ Push Service    │
│                 │    │                  │    │ (FCM/Mozilla)   │
├─────────────────┤    ├──────────────────┤    ├─────────────────┤
│ • Send push     │───▶│ • Service Worker │◀───│ • Deliver push  │
│   notifications │    │ • Handle clicks  │    │   messages      │
│ • Store tokens  │    │ • Deep linking   │    │ • Queue offline │
│ • VAPID keys    │    │ • Offline sync   │    │   messages      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Files Created

### Core Implementation
- `public/sw.js` - Service worker for handling push notifications
- `app/lib/hooks/usePushNotifications.ts` - React hook for push notification management
- `app/lib/hooks/useNotificationHandler.ts` - Deep linking and notification click handling
- `app/components/PushNotificationPrompt.tsx` - UI components for permission requests

### Integration
- `app/lib/providers/app-context.tsx` - Updated with push notification context
- `app/components/AppLayout.tsx` - Added notification prompt
- `app/routes/profile.tsx` - Added notification settings toggle

### Development Tools
- `app/components/PushNotificationTester.tsx` - Testing components (dev only)

## Backend Requirements

### 1. VAPID Keys
Your Go backend needs to generate VAPID (Voluntary Application Server Identification) keys:

```go
// Example Go code for generating VAPID keys
import "github.com/SherClockHolmes/webpush-go"

// Generate once and store securely
privateKey, publicKey, err := webpush.GenerateVAPIDKeys()
```

### 2. Environment Variables
Add to your frontend `.env` file:

```bash
# VAPID public key from your Go backend
VITE_VAPID_PUBLIC_KEY=your_vapid_public_key_here

# Existing Stream keys
VITE_STREAM_API_KEY=your_stream_api_key
VITE_STREAM_APP_ID=your_stream_app_id
```

### 3. Backend Push Sending
Your Go backend should send push notifications like this:

```go
// Example notification payload
type PushNotification struct {
    Title string `json:"title"`
    Body  string `json:"body"`
    Icon  string `json:"icon,omitempty"`
    URL   string `json:"url,omitempty"`
    Type  string `json:"type,omitempty"`
    Data  map[string]interface{} `json:"data,omitempty"`
}

// Send to registered web push tokens
func SendWebPushNotification(subscription, payload string) error {
    // Use webpush-go library to send notifications
    // to the subscription endpoint
}
```

## Usage

### 1. User Flow
1. User logs in and completes profile setup
2. Push notification prompt appears automatically
3. User grants permission and subscribes
4. Token is registered with backend via existing API
5. Backend can now send push notifications

### 2. Notification Types
The system supports different notification types with custom handling:

- **lesson** - New lesson available
- **live_class** - Live class starting soon
- **chat** - New chat message
- **announcement** - Important announcements
- **course_completion** - Course progress updates

### 3. Deep Linking
Notifications automatically navigate to relevant content:

```javascript
// Example notification data for deep linking
{
  type: 'lesson',
  groupId: '123',
  cohortId: '456',
  courseId: '789',
  lessonId: '101',
  url: '/groups/123/cohorts/456/courses/789/lessons/101'
}
```

## Development & Testing

### 1. Development Tools
In development mode, you'll see:
- **Push Status Indicator** (top-left) - Shows current push notification status
- **Test Notification Button** (bottom-left) - Send test notifications

### 2. Testing Notifications
1. Enable notifications in the app
2. Click the test button (bottom-left in dev mode)
3. Select notification type and send
4. Click the notification to test deep linking

### 3. Browser Developer Tools
- **Application > Service Workers** - Check service worker status
- **Application > Storage > Local Storage** - View stored preferences
- **Console** - See push notification logs

## Browser Support

### Supported Browsers
- ✅ Chrome 50+
- ✅ Firefox 44+
- ✅ Safari 16+ (macOS 13+, iOS 16.4+)
- ✅ Edge 17+

### Requirements
- HTTPS (required for service workers)
- User gesture for permission request
- Service worker support

## Troubleshooting

### Common Issues

1. **"Push notifications not supported"**
   - Check browser compatibility
   - Ensure HTTPS is enabled
   - Verify service worker registration

2. **Permission denied**
   - User must manually reset in browser settings
   - Clear site data and try again
   - Check for browser notification blocking

3. **Service worker not registering**
   - Check console for errors
   - Verify `/sw.js` is accessible
   - Ensure proper MIME type (application/javascript)

4. **Notifications not appearing**
   - Check browser notification settings
   - Verify VAPID key configuration
   - Test with browser developer tools

### Debug Commands

```javascript
// Check service worker status
navigator.serviceWorker.getRegistrations().then(console.log);

// Check notification permission
console.log('Permission:', Notification.permission);

// Test notification
new Notification('Test', { body: 'This is a test' });

// Check push subscription
navigator.serviceWorker.ready.then(reg => 
  reg.pushManager.getSubscription().then(console.log)
);
```

## Security Considerations

1. **VAPID Keys** - Keep private key secure on backend
2. **Token Storage** - Tokens are stored securely via existing API
3. **Permission Respect** - Always respect user's notification preferences
4. **Data Validation** - Validate all notification data on backend

## Performance

- Service worker caches essential resources
- Notifications work offline
- Background sync for read status
- Minimal impact on app performance

## Next Steps

1. **Backend Integration** - Implement VAPID keys and push sending
2. **Notification Preferences** - Add granular notification settings
3. **Rich Notifications** - Add images and action buttons
4. **Analytics** - Track notification engagement
5. **A/B Testing** - Test notification content and timing

## API Integration

The system uses the existing API endpoint:
- `POST /notifications/device-tokens` - Register push tokens
- `POST /notifications/{id}/read` - Mark notifications as read

Token format for web push:
```json
{
  "platform": "web",
  "token": "{\"endpoint\":\"...\",\"keys\":{\"p256dh\":\"...\",\"auth\":\"...\"}}"
}
```
