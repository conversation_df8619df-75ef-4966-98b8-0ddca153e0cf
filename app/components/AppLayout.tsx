import { Outlet, useNavigate, useLocation } from "react-router";
import { useEffect } from "react";

import { Sidebar } from "./Sidebar";
import { PushNotificationPrompt } from "./PushNotificationPrompt";
import {
  PushNotificationTester,
  PushNotificationStatus,
} from "./PushNotificationTester";

import { useMyGroups, useProfile } from "~/lib/api/client-queries";
import { useNotificationHandler } from "~/lib/hooks/useNotificationHandler";

export default function AppLayout() {
  const navigate = useNavigate();
  const location = useLocation();

  // Initialize notification handler for deep linking
  useNotificationHandler();

  // Fetch profile & groups in parallel via TanStack Query hooks
  const {
    data: profileResponse,
    isLoading: profileLoading,
    isError: profileError,
  } = useProfile();

  const {
    data: myGroupsResponse,
    isLoading: groupsLoading,
    isError: groupsError,
  } = useMyGroups();

  // Derive usable data structures
  const profile = profileResponse?.data ?? null;

  const groups = myGroupsResponse
    ? myGroupsResponse.groups.order.map(
        (id) => myGroupsResponse.groups.byId[id]
      )
    : [];

  // Check if profile is incomplete and redirect to setup
  useEffect(() => {
    if (profile && (!profile.firstName || !profile.lastName)) {
      // Don't redirect if we're already on the setup-profile page
      if (location.pathname !== "/setup-profile") {
        navigate("/setup-profile", { replace: true });
      }
    }
  }, [profile, navigate, location.pathname]);

  // Handle loading & error states (basic)
  if (profileLoading || groupsLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-zinc-900 text-zinc-400">
        Loading...
      </div>
    );
  }

  if (profileError || groupsError) {
    return (
      <div className="flex items-center justify-center h-screen bg-zinc-900 text-red-500">
        Failed to load user data.
      </div>
    );
  }

  // Don't show sidebar on setup-profile page
  const isSetupProfile = location.pathname === "/setup-profile";

  // Don't show sidebar on lesson pages
  const isLessonPage =
    location.pathname.includes("/courses/") &&
    location.pathname.includes("/lessons/");

  return (
    <div className="flex h-screen bg-black">
      {!isSetupProfile && !isLessonPage && (
        <Sidebar groups={groups} profile={profile} />
      )}
      <div className="flex-1 overflow-auto">
        <Outlet />
      </div>

      {/* Push notification prompt - only show after user is logged in and profile is complete */}
      {profile && profile.firstName && profile.lastName && (
        <PushNotificationPrompt />
      )}

      {/* Development tools - only show in development */}
      <PushNotificationTester />
      <PushNotificationStatus />
    </div>
  );
}
