import { useState } from 'react';
import { Bell, Send, TestTube } from 'lucide-react';
import { createTestNotification } from '~/lib/hooks/useNotificationHandler';

// This component is for development/testing purposes only
// Remove or hide in production
export function PushNotificationTester() {
  const [selectedType, setSelectedType] = useState('lesson');
  const [isVisible, setIsVisible] = useState(false);

  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  const testNotificationTypes = [
    { value: 'lesson', label: 'New Lesson' },
    { value: 'live_class', label: 'Live Class' },
    { value: 'chat', label: 'Chat Message' },
    { value: 'announcement', label: 'Announcement' },
  ];

  const sendTestNotification = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;
      const testData = createTestNotification(selectedType);
      
      // Simulate a push event by directly calling the service worker
      registration.showNotification(testData.title, {
        body: testData.body,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'test-notification',
        data: testData,
        actions: testData.actions || [],
      });
    } catch (error) {
      console.error('Failed to send test notification:', error);
      alert('Failed to send test notification. Make sure notifications are enabled.');
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 z-50 bg-yellow-500 text-white p-3 rounded-full shadow-lg hover:bg-yellow-600 transition-colors"
        title="Test Push Notifications (Dev Only)"
      >
        <TestTube className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-gray-900 flex items-center gap-2">
          <TestTube className="w-4 h-4" />
          Test Notifications
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600"
        >
          ×
        </button>
      </div>
      
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Notification Type
          </label>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            {testNotificationTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>
        
        <button
          onClick={sendTestNotification}
          className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors flex items-center justify-center gap-2"
        >
          <Send className="w-4 h-4" />
          Send Test Notification
        </button>
        
        <p className="text-xs text-gray-500">
          This will show a test notification. Make sure you've enabled notifications first.
        </p>
      </div>
    </div>
  );
}

// Component to show push notification status in development
export function PushNotificationStatus() {
  if (import.meta.env.PROD) {
    return null;
  }

  const [status, setStatus] = useState<{
    supported: boolean;
    permission: NotificationPermission;
    swRegistered: boolean;
  }>({
    supported: false,
    permission: 'default',
    swRegistered: false,
  });

  const checkStatus = async () => {
    const supported = 'serviceWorker' in navigator && 'PushManager' in window && 'Notification' in window;
    const permission = supported ? Notification.permission : 'denied';
    
    let swRegistered = false;
    try {
      const registration = await navigator.serviceWorker.getRegistration();
      swRegistered = !!registration;
    } catch (error) {
      swRegistered = false;
    }

    setStatus({ supported, permission, swRegistered });
  };

  return (
    <div className="fixed top-4 left-4 z-40 bg-gray-900 text-white p-3 rounded-lg text-xs max-w-xs">
      <div className="flex items-center gap-2 mb-2">
        <Bell className="w-4 h-4" />
        <span className="font-semibold">Push Status (Dev)</span>
        <button
          onClick={checkStatus}
          className="ml-auto text-gray-300 hover:text-white"
        >
          ↻
        </button>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>Supported:</span>
          <span className={status.supported ? 'text-green-400' : 'text-red-400'}>
            {status.supported ? 'Yes' : 'No'}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Permission:</span>
          <span className={
            status.permission === 'granted' ? 'text-green-400' :
            status.permission === 'denied' ? 'text-red-400' : 'text-yellow-400'
          }>
            {status.permission}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>SW Registered:</span>
          <span className={status.swRegistered ? 'text-green-400' : 'text-red-400'}>
            {status.swRegistered ? 'Yes' : 'No'}
          </span>
        </div>
      </div>
    </div>
  );
}
