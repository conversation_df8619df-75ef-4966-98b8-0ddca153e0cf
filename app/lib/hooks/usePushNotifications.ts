import { useState, useEffect, useCallback } from "react";
import { useRegisterPushNotificationToken } from "~/lib/api/client-queries";

export type PushPermissionState = "default" | "granted" | "denied";

export interface PushNotificationState {
  permission: PushPermissionState;
  isSupported: boolean;
  isSubscribed: boolean;
  isLoading: boolean;
  error: string | null;
  subscription: PushSubscription | null;
}

export interface UsePushNotificationsReturn extends PushNotificationState {
  requestPermission: () => Promise<boolean>;
  subscribe: () => Promise<boolean>;
  unsubscribe: () => Promise<boolean>;
  refreshSubscription: () => Promise<void>;
}

// VAPID public key - this should be provided by your backend team
// For now using a placeholder - replace with actual VAPID key from your Go backend
const VAPID_PUBLIC_KEY =
  process.env.VITE_VAPID_PUBLIC_KEY || "YOUR_VAPID_PUBLIC_KEY_HERE";

function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, "+").replace(/_/g, "/");
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

export function usePushNotifications(): UsePushNotificationsReturn {
  const [state, setState] = useState<PushNotificationState>({
    permission: "default",
    isSupported: false,
    isSubscribed: false,
    isLoading: false,
    error: null,
    subscription: null,
  });

  const { mutateAsync: registerToken } = useRegisterPushNotificationToken();

  // Check if push notifications are supported
  useEffect(() => {
    const isSupported =
      "serviceWorker" in navigator &&
      "PushManager" in window &&
      "Notification" in window;

    setState((prev) => ({
      ...prev,
      isSupported,
      permission: isSupported ? Notification.permission : "denied",
    }));

    if (isSupported) {
      checkExistingSubscription();
    }
  }, []);

  const checkExistingSubscription = useCallback(async () => {
    try {
      const registration = await navigator.serviceWorker.ready;
      const subscription = await registration.pushManager.getSubscription();

      setState((prev) => ({
        ...prev,
        isSubscribed: !!subscription,
        subscription,
      }));
    } catch (error) {
      console.error("Error checking existing subscription:", error);
      setState((prev) => ({
        ...prev,
        error: "Failed to check subscription status",
      }));
    }
  }, []);

  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!state.isSupported) {
      setState((prev) => ({
        ...prev,
        error: "Push notifications not supported",
      }));
      return false;
    }

    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const permission = await Notification.requestPermission();
      setState((prev) => ({ ...prev, permission, isLoading: false }));

      return permission === "granted";
    } catch (error) {
      console.error("Error requesting permission:", error);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: "Failed to request permission",
      }));
      return false;
    }
  }, [state.isSupported]);

  const subscribe = useCallback(async (): Promise<boolean> => {
    if (!state.isSupported || state.permission !== "granted") {
      return false;
    }

    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Register service worker if not already registered
      let registration = await navigator.serviceWorker.getRegistration();
      if (!registration) {
        registration = await navigator.serviceWorker.register("/sw.js");
        await navigator.serviceWorker.ready;
      }

      // Subscribe to push notifications
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(VAPID_PUBLIC_KEY),
      });

      // Register token with backend
      const subscriptionData = subscription.toJSON();
      await registerToken({
        platform: "web",
        token: JSON.stringify(subscriptionData),
      });

      setState((prev) => ({
        ...prev,
        isSubscribed: true,
        subscription,
        isLoading: false,
      }));

      return true;
    } catch (error) {
      console.error("Error subscribing to push notifications:", error);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: "Failed to subscribe to notifications",
      }));
      return false;
    }
  }, [state.isSupported, state.permission, registerToken]);

  const unsubscribe = useCallback(async (): Promise<boolean> => {
    if (!state.subscription) {
      return true;
    }

    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      await state.subscription.unsubscribe();

      setState((prev) => ({
        ...prev,
        isSubscribed: false,
        subscription: null,
        isLoading: false,
      }));

      return true;
    } catch (error) {
      console.error("Error unsubscribing from push notifications:", error);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: "Failed to unsubscribe from notifications",
      }));
      return false;
    }
  }, [state.subscription]);

  const refreshSubscription = useCallback(async (): Promise<void> => {
    await checkExistingSubscription();
  }, [checkExistingSubscription]);

  return {
    ...state,
    requestPermission,
    subscribe,
    unsubscribe,
    refreshSubscription,
  };
}
