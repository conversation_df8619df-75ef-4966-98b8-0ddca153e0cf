import { useEffect } from 'react';
import { useNavigate } from 'react-router';
import { useAppContext } from '~/lib/providers/app-context';

export interface NotificationClickData {
  type?: string;
  url?: string;
  notificationId?: string;
  groupId?: string;
  cohortId?: string;
  courseId?: string;
  lessonId?: string;
  moduleId?: string;
  chatChannelId?: string;
}

export function useNotificationHandler() {
  const navigate = useNavigate();
  const { setGroupId, setCohortId } = useAppContext();

  useEffect(() => {
    // Handle messages from service worker (notification clicks)
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'NOTIFICATION_CLICK') {
        handleNotificationClick(event.data.notificationData, event.data.url);
      }
    };

    // Handle direct navigation from URL parameters (when app is opened from notification)
    const handleUrlNavigation = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const notificationData = urlParams.get('notification_data');
      
      if (notificationData) {
        try {
          const data = JSON.parse(decodeURIComponent(notificationData));
          handleNotificationClick(data);
          
          // Clean up URL parameters
          const newUrl = new URL(window.location.href);
          newUrl.searchParams.delete('notification_data');
          window.history.replaceState({}, '', newUrl.toString());
        } catch (error) {
          console.error('Failed to parse notification data from URL:', error);
        }
      }
    };

    navigator.serviceWorker?.addEventListener('message', handleMessage);
    handleUrlNavigation();

    return () => {
      navigator.serviceWorker?.removeEventListener('message', handleMessage);
    };
  }, [navigate, setGroupId, setCohortId]);

  const handleNotificationClick = (data: NotificationClickData, fallbackUrl?: string) => {
    console.log('Handling notification click:', data);

    // Set context if group/cohort info is available
    if (data.groupId) {
      setGroupId(data.groupId);
    }
    if (data.cohortId) {
      setCohortId(data.cohortId);
    }

    // Navigate based on notification type
    switch (data.type) {
      case 'lesson':
        if (data.groupId && data.cohortId && data.courseId && data.lessonId) {
          navigate(
            `/groups/${data.groupId}/cohorts/${data.cohortId}/courses/${data.courseId}/lessons/${data.lessonId}`
          );
        } else if (data.url) {
          navigate(data.url);
        }
        break;

      case 'live_class':
        if (data.groupId && data.cohortId && data.moduleId) {
          navigate(`/groups/${data.groupId}/cohorts/${data.cohortId}/modules/${data.moduleId}`);
        } else if (data.url) {
          navigate(data.url);
        }
        break;

      case 'chat':
        if (data.chatChannelId) {
          navigate(`/chat?channel=${data.chatChannelId}`);
        } else {
          navigate('/chat');
        }
        break;

      case 'course_completion':
      case 'course_progress':
        if (data.groupId && data.cohortId && data.courseId) {
          navigate(`/groups/${data.groupId}/cohorts/${data.cohortId}/courses/${data.courseId}`);
        } else if (data.url) {
          navigate(data.url);
        }
        break;

      case 'group_activity':
      case 'announcement':
        if (data.groupId) {
          navigate(`/groups/${data.groupId}`);
        } else {
          navigate('/notifications');
        }
        break;

      case 'profile_update':
      case 'system':
        navigate('/profile');
        break;

      default:
        // Fallback navigation
        if (data.url) {
          navigate(data.url);
        } else if (fallbackUrl) {
          navigate(fallbackUrl);
        } else {
          navigate('/notifications');
        }
        break;
    }

    // Mark notification as read if we have the ID
    if (data.notificationId) {
      markNotificationAsRead(data.notificationId);
    }
  };

  const markNotificationAsRead = async (notificationId: string) => {
    try {
      // This would typically use the existing useReadNotification hook
      // but since we're in a utility function, we'll make a direct API call
      await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      // Store for background sync when online
      storeOfflineNotificationRead(notificationId);
    }
  };

  const storeOfflineNotificationRead = (notificationId: string) => {
    try {
      const pending = JSON.parse(localStorage.getItem('pending_notification_reads') || '[]');
      if (!pending.includes(notificationId)) {
        pending.push(notificationId);
        localStorage.setItem('pending_notification_reads', JSON.stringify(pending));
      }
    } catch (error) {
      console.error('Failed to store offline notification read:', error);
    }
  };

  return {
    handleNotificationClick,
  };
}

// Utility function to generate notification URLs with data
export function generateNotificationUrl(baseUrl: string, data: NotificationClickData): string {
  const url = new URL(baseUrl, window.location.origin);
  url.searchParams.set('notification_data', encodeURIComponent(JSON.stringify(data)));
  return url.toString();
}

// Utility function to create notification payload for testing
export function createTestNotification(type: string, data: Partial<NotificationClickData> = {}) {
  const testNotifications = {
    lesson: {
      title: 'New Lesson Available',
      body: 'A new lesson has been added to your course',
      type: 'lesson',
      url: '/groups/1/cohorts/1/courses/1/lessons/1',
      ...data,
    },
    live_class: {
      title: 'Live Class Starting Soon',
      body: 'Your live class starts in 15 minutes',
      type: 'live_class',
      url: '/groups/1/cohorts/1/modules/1',
      ...data,
    },
    chat: {
      title: 'New Message',
      body: 'You have a new message in your group chat',
      type: 'chat',
      url: '/chat',
      ...data,
    },
    announcement: {
      title: 'Important Announcement',
      body: 'There is a new announcement from your instructor',
      type: 'announcement',
      url: '/notifications',
      ...data,
    },
  };

  return testNotifications[type as keyof typeof testNotifications] || testNotifications.announcement;
}
