// Migration utility to help transition from Web Push API to Firebase
// This can be removed after migration is complete

export function migratePushNotificationSettings() {
  try {
    // Check if user had previously dismissed the old push notification prompt
    const oldDismissed = localStorage.getItem('push-notification-dismissed');
    
    if (oldDismissed) {
      // Migrate to new Firebase-based setting
      localStorage.setItem('firebase-push-notification-dismissed', oldDismissed);
      
      // Clean up old setting
      localStorage.removeItem('push-notification-dismissed');
      
      console.log('Migrated push notification settings to Firebase');
    }

    // Check for old Web Push subscription data
    const oldSubscriptionData = localStorage.getItem('web-push-subscription');
    if (oldSubscriptionData) {
      console.log('Found old Web Push subscription, will need to re-register with Firebase');
      
      // Clean up old subscription data
      localStorage.removeItem('web-push-subscription');
    }

    // Check for pending notification reads
    const pendingReads = localStorage.getItem('pending_notification_reads');
    if (pendingReads) {
      console.log('Found pending notification reads, these will be synced on next app load');
      // Keep these as they're still valid
    }

  } catch (error) {
    console.error('Error during push notification migration:', error);
  }
}

// Helper to check if user needs to re-register for push notifications
export function needsPushNotificationReregistration(): boolean {
  // If user had old Web Push but no Firebase token, they need to re-register
  const hasFirebaseToken = !!localStorage.getItem('fcm_token');
  const hadOldPushSettings = localStorage.getItem('firebase-push-notification-dismissed') === 'true';
  
  return !hasFirebaseToken && !hadOldPushSettings;
}

// Clean up any old push notification related data
export function cleanupOldPushNotificationData() {
  const oldKeys = [
    'push-notification-dismissed',
    'web-push-subscription', 
    'vapid-subscription-data',
    'push-manager-subscription'
  ];

  oldKeys.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key);
      console.log(`Cleaned up old push notification data: ${key}`);
    }
  });
}
